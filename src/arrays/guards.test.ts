import { describe, expect, test } from 'bun:test'
import { isArray, isEmptyArray, isEmptyIterable, isIterable } from './guards'

describe('isArray', () => {
    test('should return true for arrays', () => {
        expect(isArray([])).toBe(true)
        expect(isArray([1, 2, 3])).toBe(true)
        expect(isArray(['a', 'b', 'c'])).toBe(true)
        expect(isArray([null, undefined])).toBe(true)
        expect(isArray([{}, []])).toBe(true)
        expect(isArray(Array.from({ length: 5 }))).toBe(true)
        expect(isArray(Array.from({ length: 3 }))).toBe(true)
    })

    test('should return false for non-arrays', () => {
        expect(isArray(null)).toBe(false)
        // @ts-expect-error
        expect(isArray()).toBe(false)
        expect(isArray('hello')).toBe(false)
        expect(isArray(123)).toBe(false)
        expect(isArray({})).toBe(false)
        expect(isArray(new Set())).toBe(false)
        expect(isArray(new Map())).toBe(false)
        expect(isArray(() => {})).toBe(false)
        expect(isArray(Symbol('test'))).toBe(false)
        expect(isArray(true)).toBe(false)
    })

    test('should provide proper type narrowing', () => {
        const value: unknown = [1, 2, 3]

        if (isArray<number>(value)) {
            // This should compile without TypeScript errors
            expect(value.length).toBe(3)
            expect(value[0]).toBe(1)
        }
    })
})

describe('isIterable', () => {
    test('should return true for iterables', () => {
        expect(isIterable([])).toBe(true)
        expect(isIterable([1, 2, 3])).toBe(true)
        expect(isIterable('hello')).toBe(true)
        expect(isIterable('')).toBe(true)
        expect(isIterable(new Set())).toBe(true)
        expect(isIterable(new Set([1, 2, 3]))).toBe(true)
        expect(isIterable(new Map())).toBe(true)
        expect(isIterable(new Map([['a', 1]]))).toBe(true)
        expect(isIterable(new Int8Array())).toBe(true)
        expect(isIterable(new Uint8Array([1, 2, 3]))).toBe(true)
    })

    test('should return true for custom iterables', () => {
        const customIterable = {
            * [Symbol.iterator]() {
                yield 1
                yield 2
                yield 3
            },
        }

        expect(isIterable(customIterable)).toBe(true)

        function * generator() {
            yield 1
            yield 2
        }

        expect(isIterable(generator())).toBe(true)
    })

    test('should return false for non-iterables', () => {
        expect(isIterable(null)).toBe(false)
        // @ts-expect-error
        expect(isIterable()).toBe(false)
        expect(isIterable(123)).toBe(false)
        expect(isIterable(true)).toBe(false)
        expect(isIterable({})).toBe(false)
        expect(isIterable({ length: 3 })).toBe(false)
        expect(isIterable(() => {})).toBe(false)
        expect(isIterable(Symbol('test'))).toBe(false)
    })

    test('should return false for objects with non-function Symbol.iterator', () => {
        const fakeIterable = {
            [Symbol.iterator]: 'not a function',
        }

        expect(isIterable(fakeIterable)).toBe(false)

        const anotherFakeIterable = {
            [Symbol.iterator]: null,
        }

        expect(isIterable(anotherFakeIterable)).toBe(false)
    })

    test('should provide proper type narrowing', () => {
        const value: unknown = [1, 2, 3]

        if (isIterable<number>(value)) {
            // This should compile without TypeScript errors
            const items: number[] = []

            for (const item of value) {
                items.push(item)
            }

            expect(items).toEqual([1, 2, 3])
        }
    })
})

describe('isEmptyArray', () => {
    test('should return true for empty arrays', () => {
        expect(isEmptyArray([])).toBe(true)
        expect(isEmptyArray([])).toBe(true)
        expect(isEmptyArray(Array.from({ length: 0 }))).toBe(true)
    })

    test('should return false for non-empty arrays', () => {
        expect(isEmptyArray([1])).toBe(false)
        expect(isEmptyArray([1, 2, 3])).toBe(false)
        expect(isEmptyArray([''])).toBe(false)
        expect(isEmptyArray([null])).toBe(false)
        expect(isEmptyArray([undefined])).toBe(false)
        expect(isEmptyArray([0])).toBe(false)
        expect(isEmptyArray([false])).toBe(false)
        expect(isEmptyArray(Array.from({ length: 1 }))).toBe(false) // Array with holes
    })
})

describe('isEmptyIterable', () => {
    test('should return true for empty iterables', () => {
        expect(isEmptyIterable([])).toBe(true)
        expect(isEmptyIterable('')).toBe(true)
        expect(isEmptyIterable(new Set())).toBe(true)
        expect(isEmptyIterable(new Map())).toBe(true)
        expect(isEmptyIterable(new Int8Array())).toBe(true)
        expect(isEmptyIterable(new Uint8Array())).toBe(true)
    })

    test('should return true for empty generators', () => {
        function * emptyGenerator() {}

        expect(isEmptyIterable(emptyGenerator())).toBe(true)

        const emptyCustomIterable = {
            * [Symbol.iterator]() {},
        }

        expect(isEmptyIterable(emptyCustomIterable)).toBe(true)
    })

    test('should return false for non-empty iterables', () => {
        expect(isEmptyIterable([1])).toBe(false)
        expect(isEmptyIterable([1, 2, 3])).toBe(false)
        expect(isEmptyIterable('a')).toBe(false)
        expect(isEmptyIterable('hello')).toBe(false)
        expect(isEmptyIterable(new Set([1]))).toBe(false)
        expect(isEmptyIterable(new Set([1, 2, 3]))).toBe(false)
        expect(isEmptyIterable(new Map([['a', 1]]))).toBe(false)
        expect(isEmptyIterable(new Int8Array([1]))).toBe(false)
        expect(isEmptyIterable(new Uint8Array([1, 2, 3]))).toBe(false)
    })

    test('should return false for non-empty generators', () => {
        function * nonEmptyGenerator() {
            yield 1
            yield 2
        }

        expect(isEmptyIterable(nonEmptyGenerator())).toBe(false)

        const nonEmptyCustomIterable = {
            * [Symbol.iterator]() {
                yield 'hello'
            },
        }

        expect(isEmptyIterable(nonEmptyCustomIterable)).toBe(false)
    })

    test('should handle edge cases', () => {
        // Array with holes (sparse array)
        const sparseArray = Array.from({ length: 3 })
        expect(isEmptyIterable(sparseArray)).toBe(false) // Has length > 0, so not empty

        // String with whitespace
        expect(isEmptyIterable(' ')).toBe(false)
        expect(isEmptyIterable('\n')).toBe(false)
        expect(isEmptyIterable('\t')).toBe(false)

        // Set/Map with falsy values
        expect(isEmptyIterable(new Set([0]))).toBe(false)
        expect(isEmptyIterable(new Set([false]))).toBe(false)
        expect(isEmptyIterable(new Set([null]))).toBe(false)
        expect(isEmptyIterable(new Map([[0, 'zero']]))).toBe(false)
    })
})

describe('integration tests', () => {
    test('should work together for type checking and emptiness', () => {
        const values: unknown[] = [
            [],
            [1, 2, 3],
            '',
            'hello',
            new Set(),
            new Set([1, 2]),
            {},
            null,
            undefined,
        ]

        values.forEach((value) => {
            if (isArray(value)) {
                const isEmpty = isEmptyArray(value)
                expect(typeof isEmpty).toBe('boolean')
            }

            if (isIterable(value)) {
                const isEmpty = isEmptyIterable(value)
                expect(typeof isEmpty).toBe('boolean')
            }
        })
    })

    test('should handle complex nested structures', () => {
        const nestedArray = [[], [1], [[2, 3]]]
        expect(isArray(nestedArray)).toBe(true)
        expect(isEmptyArray(nestedArray)).toBe(false)
        expect(isIterable(nestedArray)).toBe(true)
        expect(isEmptyIterable(nestedArray)).toBe(false)

        const emptyNestedArray: never[][] = []

        expect(isArray(emptyNestedArray)).toBe(true)
        expect(isEmptyArray(emptyNestedArray)).toBe(true)
        expect(isIterable(emptyNestedArray)).toBe(true)
        expect(isEmptyIterable(emptyNestedArray)).toBe(true)
    })
})

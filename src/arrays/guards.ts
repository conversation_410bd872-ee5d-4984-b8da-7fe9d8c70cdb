/**
 * Checks if a value is an array.
 *
 * This function is a type-safe wrapper around Array.isArray() that provides
 * proper TypeScript type narrowing.
 *
 * @template T - The type of array elements
 * @param value - The value to check
 * @returns True if the value is an array, false otherwise
 *
 * @example
 * ```typescript
 * isArray([1, 2, 3]) // true
 * isArray('hello') // false
 * isArray(null) // false
 * isArray({}) // false
 *
 * // Type narrowing example
 * const value: unknown = [1, 2, 3]
 * if (isArray<number>(value)) {
 *   // value is now typed as number[]
 *   console.log(value.length) // TypeScript knows this is safe
 * }
 * ```
 */
export function isArray<T = unknown>(value: unknown): value is T[] {
    return Array.isArray(value)
}

/**
 * Checks if a value is iterable.
 *
 * An iterable is any object that implements the Symbol.iterator method,
 * such as arrays, strings, sets, maps, etc.
 *
 * @template T - The type of iterable elements
 * @param value - The value to check
 * @returns True if the value is iterable, false otherwise
 *
 * @example
 * ```typescript
 * isIterable([1, 2, 3]) // true
 * isIterable('hello') // true
 * isIterable(new Set([1, 2, 3])) // true
 * isIterable(new Map()) // true
 * isIterable({}) // false
 * isIterable(null) // false
 * isIterable(42) // false
 *
 * // Type narrowing example
 * const value: unknown = [1, 2, 3]
 * if (isIterable<number>(value)) {
 *   // value is now typed as Iterable<number>
 *   for (const item of value) {
 *     console.log(item) // TypeScript knows item is number
 *   }
 * }
 * ```
 */
export function isIterable<T = unknown>(value: unknown): value is Iterable<T> {
    return value != null && Symbol.iterator in Object(value) && typeof (value as any)[Symbol.iterator] === 'function'
}

/**
 * Checks if an array is empty.
 *
 * This function checks if the provided array has zero elements.
 *
 * @template T - The type of array elements
 * @param value - The array to check
 * @returns True if the array is empty (length === 0), false otherwise
 *
 * @example
 * ```typescript
 * isEmptyArray([]) // true
 * isEmptyArray([1, 2, 3]) // false
 * isEmptyArray(['']) // false (contains one empty string)
 * isEmptyArray([null]) // false (contains one null value)
 * ```
 */
export function isEmptyArray<T>(value: T[]) {
    return value.length === 0
}

/**
 * Checks if an iterable is empty.
 *
 * This function checks if the provided iterable has no elements by
 * calling its iterator and checking if the first call to next() is done.
 *
 * @template T - The type of iterable elements
 * @param value - The iterable to check
 * @returns True if the iterable is empty, false otherwise
 *
 * @example
 * ```typescript
 * isEmptyIterable([]) // true
 * isEmptyIterable([1, 2, 3]) // false
 * isEmptyIterable('') // true
 * isEmptyIterable('hello') // false
 * isEmptyIterable(new Set()) // true
 * isEmptyIterable(new Set([1, 2])) // false
 * isEmptyIterable(new Map()) // true
 *
 * // Generator example
 * function* emptyGenerator() {}
 * function* nonEmptyGenerator() { yield 1 }
 * isEmptyIterable(emptyGenerator()) // true
 * isEmptyIterable(nonEmptyGenerator()) // false
 * ```
 */
export function isEmptyIterable<T>(value: Iterable<T>) {
    return value[Symbol.iterator]().next().done ?? true
}

/**
 * Checks if a value is an array.
 *
 * @template T - The type of array elements
 * @param value - The value to check
 * @returns True if the value is an array, false otherwise
 */
export function isArray<T = unknown>(value: unknown): value is T[] {
    return Array.isArray(value)
}

/**
 * Checks if a value is iterable.
 *
 * @template T - The type of iterable elements
 * @param value - The value to check
 * @returns True if the value is iterable, false otherwise
 */
export function isIterable<T = unknown>(value: unknown): value is Iterable<T> {
    return value != null && Symbol.iterator in Object(value) && typeof (value as any)[Symbol.iterator] === 'function'
}

/**
 * Checks if an array is empty.
 *
 * @template T - The type of array elements
 * @param value - The array to check
 * @returns True if the array is empty, false otherwise
 */
export function isEmptyArray<T>(value: T[]) {
    return value.length === 0
}

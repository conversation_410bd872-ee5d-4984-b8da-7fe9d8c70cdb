export function isArray<T = unknown>(value: unknown): value is T[] {
    return Array.isArray(value)
}

export function isIterable<T = unknown>(value: unknown): value is Iterable<T> {
    return value != null && typeof value === 'object' && Symbol.iterator in value && typeof value[Symbol.iterator] === 'function'
}

export function isEmptyIterable<T>(value: Iterable<T>) {
    return value[Symbol.iterator]().next().done ?? true
}
